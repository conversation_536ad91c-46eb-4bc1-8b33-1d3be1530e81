import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for BrainSpace application."""

    # Database configuration
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL", "postgresql://localhost/brainspace")

    # BRAIN API configuration
    BRAIN_API_BASE_URL: str = os.getenv(
        "BRAIN_API_BASE_URL", "https://api.worldquantbrain.com")
    BRAIN_CREDENTIALS_FILE: str = os.getenv(
        "BRAIN_CREDENTIALS_FILE", "~/.brain_credentials")

    # Telegram bot configuration
    TELEGRAM_BOT_TOKEN: Optional[str] = os.getenv("TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHAT_ID: Optional[str] = os.getenv("TELEGRAM_CHAT_ID")

    # Session configuration
    SESSION_REFRESH_THRESHOLD: int = int(
        os.getenv("SESSION_REFRESH_THRESHOLD", "300"))  # 5 minutes
    SESSION_CHECK_INTERVAL: int = int(
        os.getenv("SESSION_CHECK_INTERVAL", "60"))  # 1 minute

    # API rate limiting
    API_CONCURRENCY_LIMIT: int = int(os.getenv("API_CONCURRENCY_LIMIT", "5"))
    API_REQUEST_TIMEOUT: int = int(os.getenv("API_REQUEST_TIMEOUT", "30"))

    @classmethod
    def validate(cls) -> None:
        """Validate required configuration."""
        if not cls.TELEGRAM_BOT_TOKEN:
            print(
                "Warning: TELEGRAM_BOT_TOKEN not set. Telegram notifications will be disabled.")
        if not cls.TELEGRAM_CHAT_ID:
            print(
                "Warning: TELEGRAM_CHAT_ID not set. Telegram notifications will be disabled.")
