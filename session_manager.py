import time
import threading
import logging
from typing import <PERSON><PERSON>
from datetime import datetime

from session_handler import BrainSessionHandler
from config import Config

logger = logging.getLogger(__name__)


class SessionManager:
    """
    Continuous session manager that monitors and maintains BRAIN API sessions.

    Features:
    - Continuous session monitoring
    - Automatic session refresh before expiry
    - Telegram notifications for session events
    - Thread-safe operation
    """

    def __init__(self):
        self.session_handler = BrainSessionHandler()
        self._running = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        self._last_check = None
        self._last_refresh = None

    def start(self) -> bool:
        """
        Start the session manager and authenticate.

        Returns:
            bool: True if started successfully, False otherwise
        """
        with self._lock:
            if self._running:
                logger.warning("Session manager is already running")
                return True

            # Attempt initial authentication
            if not self.session_handler.authenticate():
                logger.error("Failed to authenticate during startup")
                return False

            self._running = True
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()

            logger.info("Session manager started successfully")
            return True

    def stop(self) -> None:
        """Stop the session manager."""
        with self._lock:
            if not self._running:
                return

            self._running = False
            logger.info("Stopping session manager...")

        # Wait for monitor thread to finish
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)

        # Logout
        self.session_handler.logout()
        logger.info("Session manager stopped")

    def _monitor_loop(self) -> None:
        """Main monitoring loop that runs in a separate thread."""
        logger.info("Session monitor started")

        while self._running:
            try:
                self._check_session()
                time.sleep(Config.SESSION_CHECK_INTERVAL)
            except Exception as e:
                logger.error(f"Error in session monitor: {e}")
                time.sleep(Config.SESSION_CHECK_INTERVAL)

        logger.info("Session monitor stopped")

    def _check_session(self) -> None:
        """Check session validity and refresh if needed."""
        self._last_check = datetime.now()

        if not self.session_handler.is_session_valid():
            logger.info("Session expired or expiring soon, refreshing...")
            self._last_refresh = datetime.now()

            if self.session_handler.refresh_session():
                logger.info("Session refreshed successfully")
            else:
                logger.error("Failed to refresh session")
                # Send error notification
                self.session_handler._send_telegram_notification(
                    "Session refresh failed - manual intervention may be required",
                    is_error=True
                )

    def get_session(self):
        """
        Get the current session, ensuring it's valid.

        Returns:
            requests.Session: The authenticated session
        """
        return self.session_handler.get_session()

    def is_running(self) -> bool:
        """Check if the session manager is running."""
        with self._lock:
            return self._running

    def get_status(self) -> dict:
        """
        Get the current status of the session manager.

        Returns:
            dict: Status information
        """
        return {
            "running": self.is_running(),
            "session_valid": self.session_handler.is_session_valid(),
            "user_id": self.session_handler.user_id,
            "token_expiry": self.session_handler.token_expiry,
            "last_check": self._last_check,
            "last_refresh": self._last_refresh
        }

    def force_refresh(self) -> bool:
        """
        Force a session refresh regardless of current validity.

        Returns:
            bool: True if refresh successful, False otherwise
        """
        logger.info("Forcing session refresh...")
        self._last_refresh = datetime.now()
        return self.session_handler.refresh_session()


# Global session manager instance
_session_manager: Optional[SessionManager] = None


def get_session_manager() -> SessionManager:
    """
    Get the global session manager instance.

    Returns:
        SessionManager: The global session manager
    """
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager


def start_session_manager() -> bool:
    """
    Start the global session manager.

    Returns:
        bool: True if started successfully, False otherwise
    """
    return get_session_manager().start()


def stop_session_manager() -> None:
    """Stop the global session manager."""
    global _session_manager
    if _session_manager:
        _session_manager.stop()
        _session_manager = None


if __name__ == "__main__":
    # Example usage of session manager
    import signal
    import sys

    Config.validate()

    def signal_handler(signum, frame):
        print("\nReceived interrupt signal, shutting down...")
        stop_session_manager()
        sys.exit(0)

    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Start session manager
    manager = get_session_manager()

    if manager.start():
        print("✅ Session manager started successfully!")
        print("Press Ctrl+C to stop...")

        try:
            # Keep the main thread alive
            while manager.is_running():
                time.sleep(1)

                # Print status every 30 seconds
                if int(time.time()) % 30 == 0:
                    status = manager.get_status()
                    print(f"Status: {status}")

        except KeyboardInterrupt:
            print("\nShutting down...")
        finally:
            manager.stop()
    else:
        print("❌ Failed to start session manager!")
        sys.exit(1)
