import psycopg2
import psycopg2.extras
from psycopg2.extensions import connection, cursor
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime
from contextlib import contextmanager

from config import Config

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database manager for BrainSpace application."""

    def __init__(self):
        self.connection_string = Config.DATABASE_URL
        self._connection: Optional[connection] = None

    @contextmanager
    def get_connection(self):
        """Get a database connection with automatic cleanup."""
        conn = None
        try:
            conn = psycopg2.connect(self.connection_string)
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    @contextmanager
    def get_cursor(self, commit: bool = True):
        """Get a database cursor with automatic cleanup."""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            try:
                yield cursor
                if commit:
                    conn.commit()
            except Exception as e:
                conn.rollback()
                logger.error(f"Database operation error: {e}")
                raise
            finally:
                cursor.close()

    def initialize_database(self) -> bool:
        """
        Initialize the database with required tables.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            with self.get_cursor() as cursor:
                # Create template table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS template (
                        id SERIAL PRIMARY KEY,
                        template TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT NOW()
                    )
                """)

                # Create alpha table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS alpha (
                        id SERIAL PRIMARY KEY,
                        alpha TEXT NOT NULL,
                        status VARCHAR(20) DEFAULT 'none' CHECK (status IN ('none', 'simulating', 'finish', 'error')),
                        simulationid TEXT,
                        result JSONB,
                        error TEXT,
                        created_at TIMESTAMP DEFAULT NOW(),
                        simulate_at TIMESTAMP,
                        result_at TIMESTAMP
                    )
                """)

                # Create alpha_reference table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS alpha_reference (
                        id SERIAL PRIMARY KEY,
                        alpha_id INTEGER REFERENCES alpha(id) ON DELETE CASCADE,
                        template_id INTEGER REFERENCES template(id) ON DELETE CASCADE
                    )
                """)

                # Create session_log table for tracking session events
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS session_log (
                        id SERIAL PRIMARY KEY,
                        event_type VARCHAR(50) NOT NULL,
                        user_id TEXT,
                        message TEXT,
                        created_at TIMESTAMP DEFAULT NOW()
                    )
                """)

                # Create indexes for better performance
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_alpha_status ON alpha(status);
                    CREATE INDEX IF NOT EXISTS idx_alpha_created_at ON alpha(created_at);
                    CREATE INDEX IF NOT EXISTS idx_session_log_created_at ON session_log(created_at);
                """)

            logger.info("Database initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            return False

    def log_session_event(self, event_type: str, user_id: Optional[str] = None, message: Optional[str] = None) -> bool:
        """
        Log a session event to the database.

        Args:
            event_type: Type of event (e.g., 'login', 'logout', 'refresh', 'error')
            user_id: User ID if available
            message: Additional message

        Returns:
            bool: True if logged successfully, False otherwise
        """
        try:
            with self.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO session_log (event_type, user_id, message)
                    VALUES (%s, %s, %s)
                """, (event_type, user_id, message))
            return True
        except Exception as e:
            logger.error(f"Failed to log session event: {e}")
            return False

    def get_recent_session_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent session events from the database.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of session events
        """
        try:
            with self.get_cursor(commit=False) as cursor:
                cursor.execute("""
                    SELECT event_type, user_id, message, created_at
                    FROM session_log
                    ORDER BY created_at DESC
                    LIMIT %s
                """, (limit,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Failed to get session events: {e}")
            return []

    def get_alpha_count_by_status(self) -> Dict[str, int]:
        """
        Get count of alphas by status.

        Returns:
            Dictionary with status counts
        """
        try:
            with self.get_cursor(commit=False) as cursor:
                cursor.execute("""
                    SELECT status, COUNT(*) as count
                    FROM alpha
                    GROUP BY status
                """)
                return {row['status']: row['count'] for row in cursor.fetchall()}
        except Exception as e:
            logger.error(f"Failed to get alpha status counts: {e}")
            return {}

    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.

        Returns:
            Dictionary with database statistics
        """
        try:
            with self.get_cursor(commit=False) as cursor:
                # Get table row counts
                cursor.execute("""
                    SELECT
                        (SELECT COUNT(*) FROM template) as template_count,
                        (SELECT COUNT(*) FROM alpha) as alpha_count,
                        (SELECT COUNT(*) FROM alpha_reference) as reference_count,
                        (SELECT COUNT(*) FROM session_log) as session_log_count
                """)
                stats = dict(cursor.fetchone())

                # Get alpha status breakdown
                stats['alpha_status'] = self.get_alpha_count_by_status()

                return stats
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}


# Global database manager instance
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """
    Get the global database manager instance.

    Returns:
        DatabaseManager: The global database manager
    """
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager


def initialize_database() -> bool:
    """
    Initialize the database with required tables.

    Returns:
        bool: True if initialization successful, False otherwise
    """
    return get_database_manager().initialize_database()


if __name__ == "__main__":
    # Example usage
    Config.validate()

    db_manager = get_database_manager()

    if db_manager.initialize_database():
        print("✅ Database initialized successfully!")

        # Log some test events
        db_manager.log_session_event(
            "test", "test_user", "Database initialization test")

        # Get stats
        stats = db_manager.get_database_stats()
        print(f"Database stats: {stats}")

        # Get recent events
        events = db_manager.get_recent_session_events(5)
        print(f"Recent events: {events}")
    else:
        print("❌ Database initialization failed!")
