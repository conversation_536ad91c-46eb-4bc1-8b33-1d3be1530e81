#!/usr/bin/env python3
"""
Example usage of BrainSpace Session Handler

This script demonstrates how to use the session handler for:
- Authentication with BRAIN API
- Biometric authentication handling
- Session management and auto-refresh
- Telegram notifications
- Database logging
"""

import time
import signal
import sys
from datetime import datetime

from config import Config
from session_handler import BrainSessionHandler
from session_manager import SessionManager, get_session_manager
from database import get_database_manager, initialize_database


def signal_handler(signum, frame):
    """Handle interrupt signals for graceful shutdown."""
    print("\nReceived interrupt signal, shutting down...")
    stop_session_manager()
    sys.exit(0)


def test_basic_authentication():
    """Test basic session handler functionality."""
    print("🔐 Testing basic authentication...")

    handler = BrainSessionHandler()

    if handler.authenticate():
        print("✅ Authentication successful!")
        print(f"User ID: {handler.user_id}")
        print(f"Token expires: {handler.token_expiry}")
        print(f"Session valid: {handler.is_session_valid()}")

        # Test API call
        try:
            session = handler.get_session()
            response = session.get(
                f"{Config.BRAIN_API_BASE_URL}/authentication")
            print(f"API test response: {response.status_code}")

            if response.status_code == 200:
                user_info = response.json()
                print(
                    f"User info: {user_info.get('user', {}).get('id', 'N/A')}")
        except Exception as e:
            print(f"API test failed: {e}")

        # Logout
        handler.logout()
        print("✅ Logout successful!")
        return True
    else:
        print("❌ Authentication failed!")
        return False


def test_session_manager():
    """Test the continuous session manager."""
    print("\n🔄 Testing session manager...")

    manager = get_session_manager()

    if manager.start():
        print("✅ Session manager started!")

        # Monitor for a few minutes
        start_time = time.time()
        while time.time() - start_time < 60:  # Run for 1 minute
            status = manager.get_status()
            print(f"Status: {status}")
            time.sleep(10)

        manager.stop()
        print("✅ Session manager stopped!")
        return True
    else:
        print("❌ Failed to start session manager!")
        return False


def test_database_integration():
    """Test database integration and logging."""
    print("\n🗄️ Testing database integration...")

    # Initialize database
    if not initialize_database():
        print("❌ Failed to initialize database!")
        return False

    db_manager = get_database_manager()

    # Test session handler with database logging
    handler = BrainSessionHandler()

    if handler.authenticate():
        print("✅ Authentication with database logging successful!")

        # Get recent session events
        events = db_manager.get_recent_session_events(5)
        print(f"Recent session events: {len(events)} found")
        for event in events:
            print(
                f"  - {event['event_type']}: {event['message']} ({event['created_at']})")

        # Get database stats
        stats = db_manager.get_database_stats()
        print(f"Database stats: {stats}")

        handler.logout()
        return True
    else:
        print("❌ Authentication failed!")
        return False


def test_telegram_notifications():
    """Test Telegram notifications (if configured)."""
    print("\n📱 Testing Telegram notifications...")

    if not Config.TELEGRAM_BOT_TOKEN or not Config.TELEGRAM_CHAT_ID:
        print("⚠️ Telegram not configured, skipping test")
        return True

    handler = BrainSessionHandler()

    # Test notification
    handler._send_telegram_notification("Test notification from BrainSpace")
    print("✅ Telegram notification sent!")

    return True


def main():
    """Main example function."""
    print("🚀 BrainSpace Session Handler Example")
    print("=" * 50)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Validate configuration
    Config.validate()

    # Test basic authentication
    if not test_basic_authentication():
        print("❌ Basic authentication test failed!")
        return

    # Test database integration
    if not test_database_integration():
        print("❌ Database integration test failed!")
        return

    # Test Telegram notifications
    if not test_telegram_notifications():
        print("❌ Telegram notification test failed!")
        return

    # Test session manager (optional - requires user interaction for biometric)
    print("\n🔄 Session manager test (optional)")
    print("This will run for 1 minute. Press Ctrl+C to stop early.")
    response = input("Run session manager test? (y/N): ")

    if response.lower() == 'y':
        test_session_manager()

    print("\n✅ All tests completed successfully!")
    print("\n📋 Summary:")
    print("- Session handler: Working")
    print("- Database integration: Working")
    print("- Telegram notifications: Working")
    print("- Session manager: Available")


if __name__ == "__main__":
    main()
