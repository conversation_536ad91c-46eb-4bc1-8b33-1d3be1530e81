## 1. Overview

This program is a **personal alpha generation and simulation system** built with Python and PostgreSQL.
It consists of:

* **Session management** (biometric login + Telegram bot notification)
* **Alpha space parser** (generate alphas from templates & parameter space, save to DB)
* **Simulator** (submit alphas to external API, track status)
* **Result fetcher** (poll for completed results, save to DB)

It is designed for **single-user operation**, **continuous simulation**, and **efficient batching/threading**.

---

## 2. Goals

* Automate alpha signal generation from templates.
* Persist simulation lifecycle in PostgreSQL.
* Prevent duplicate re-simulations of alphas already in DB.
* Provide resilient simulation loop (simulator + fetcher).

---

## 3. Non-Goals

* Multi-user authentication.
* UI/dashboard (interface for viewing results will be separate component).
* Error alpha retry (errors remain until manual inspection).

---

## 4. Components

Perfect — you want **Session Management** to be more detailed and robust.
Here’s an **enhanced version** of that section:

---

### 4.1 Session Management

The session management module is responsible for **authenticating with the external API**, **managing session lifecycle**, and **notifying the user via Telegram bot**.

#### Authentication Flow

1. **Initial login**
   * Attempt authentication with the standard API.
   * If the response is `401 Unauthorized` with `WWW-Authenticate: persona` header:

     * Request a biometric-linked login URL ("persona link").
     * Send this link to the user via Telegram bot.

2. **User confirmation**
   * The persona link may expire quickly.
   * If expired, the bot waits for the user to send `/login` in Telegram, which will trigger a new persona link request and resend it.

3. **Session token storage**
   * On successful login, store session token securely in disk with expiry timestamp.
   * Provide the token transparently to all downstream API calls (simulator, fetcher).

#### Session Lifecycle
* **Token refresh**
  * Re-trigger login flow when expired.

* **Expiry notifications**

  * Notify user via Telegram bot at:
    * **1 hour before expiry**
    * **30 minutes before expiry**
    * **On expiry** (prompting `/login` if needed).
* **Failure handling**
  * On repeated 401 errors, assume session invalid → restart login flow.
  * Always notify user via Telegram for manual intervention.

#### Responsibilities
* Provide a **session object** usable across components (simulator & fetcher).
* Expose helper functions:

  * `get_session()` → returns valid token or triggers login if needed.
  * `is_expired()` → checks if token is still valid.
  * `notify_tg(message)` → send Telegram notification.


---

### 4.2 Alpha Space Parser (Single-run script)

* Input:

  * Template (Python snippet with placeholders)
  * Parameter space (dictionary of substitutions)
  * Settings (universe, constraints, etc.)
* Output:

  * All possible alpha code variants (batched to avoid explosion).
* Store:

  * Template → `template` table.
  * Alpha code → `alpha` table (`status=none`).
  * Reference mapping → `alpha_reference` table.
* Deduplication:

  * Skip alpha if identical code already exists (no hash, exact text compare).
* **Future TODO (not v1):**

  * Cleanup parser output by stripping comments, redundant newlines, extra spaces.
  * This ensures alpha simplicity and prevents duplicates caused by formatting.

---

### 4.3 Simulator (Continuous loop)

* Fetch `alpha` rows with `status=none`.
* Submit in batches with threading (respect API concurrency limits).
* Update `status=simulating` and record `simulation_id`.
* If API fails → set `status=error` and save error message.
* **No retries for error alphas.**

---

### 4.4 Result Fetcher (Continuous loop)

* Fetch `alpha` rows with `status=simulating`.
* Poll API using `simulation_id`.
* On completion:

  * Update `status=finish`.
  * Store `result` and `result_at`.
* On error:

  * Save error and mark `status=error`.

---

## 5. Database Schema

### `template`

| Field       | Type      | Notes           |
| ----------- | --------- | --------------- |
| id          | SERIAL PK |                 |
| template    | TEXT      | Template string |
| created\_at | TIMESTAMP | default now()   |

### `alpha`

| Field        | Type      | Notes                                   |
| ------------ | --------- | --------------------------------------- |
| id           | SERIAL PK |                                         |
| alpha        | TEXT      | generated alpha code                    |
| status       | ENUM      | `none`, `simulating`, `finish`, `error` |
| simulationid | TEXT      | external API simulation ID              |
| result       | JSONB     | simulation results                      |
| error        | TEXT      | error message                           |
| created\_at  | TIMESTAMP | default now()                           |
| simulate\_at | TIMESTAMP | set when sent to simulator              |
| result\_at   | TIMESTAMP | set when results retrieved              |

### `alpha_reference`

| Field        | Type      | Notes               |
| ------------ | --------- | ------------------- |
| id           | SERIAL PK |                     |
| alpha\_id    | INT       | FK → `alpha(id)`    |
| template\_id | INT       | FK → `template(id)` |

---

## 6. Threading & Batching

* **Parser:** generate in batches to avoid memory blowup.
* **Simulator:** thread pool, configurable concurrency (e.g. 5–10 threads).
* **Fetcher:** threaded polling with backoff.

---

## 7. Success Metrics

* Parser generates N alphas without OOM (tested in batches).
* Simulator handles API limits smoothly.
* No duplicate simulations (exact text match).
* Fetcher retrieves >95% results successfully.
* Error alphas clearly marked, not retried.

---

## 8. Risks & Mitigations

* **API concurrency limit** → handled with batching + throttling.
* **Parser explosion** → batching + optional pruning.
* **Session expiry** → auto-refresh with Telegram alert.
* **Error accumulation** → left for manual review (no retries).