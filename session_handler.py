import json
import time
import requests
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path
from urllib.parse import urljoin
import logging

from config import Config
from database import get_database_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BrainSessionHandler:
    """
    Session handler for BRAIN API with biometric authentication support.

    Features:
    - Biometric authentication with browser interaction
    - Session token management and auto-refresh
    - Telegram bot notifications
    - Persistent session storage
    """

    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = Config.API_REQUEST_TIMEOUT
        self.base_url = Config.BRAIN_API_BASE_URL
        self.credentials_file = Path(
            Config.BRAIN_CREDENTIALS_FILE).expanduser()
        self.token_expiry: Optional[datetime] = None
        self.user_id: Optional[str] = None
        self._telegram_bot = None
        self._db_manager = get_database_manager()

        # Initialize Telegram bot if configured
        if Config.TELEGRAM_BOT_TOKEN and Config.TELEGRAM_CHAT_ID:
            try:
                from telegram import Bot
                self._telegram_bot = Bot(token=Config.TELEGRAM_BOT_TOKEN)
            except ImportError:
                logger.warning(
                    "python-telegram-bot not installed. Telegram notifications disabled.")

    def _load_credentials(self) -> tuple[str, str]:
        """Load credentials from the credentials file."""
        if not self.credentials_file.exists():
            raise FileNotFoundError(
                f"Credentials file not found: {self.credentials_file}. "
                f"Please create it with format: [\"email\", \"password\"]"
            )

        try:
            with open(self.credentials_file, 'r') as f:
                credentials = json.load(f)

            if not isinstance(credentials, list) or len(credentials) != 2:
                raise ValueError(
                    "Credentials file must contain a list with [email, password]")

            return credentials[0], credentials[1]
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in credentials file: {e}")

    def _send_telegram_notification(self, message: str, is_error: bool = False) -> None:
        """Send notification via Telegram bot."""
        if not self._telegram_bot:
            logger.info(f"Telegram notification: {message}")
            return

        try:
            prefix = "❌ ERROR" if is_error else "✅ SUCCESS"
            formatted_message = f"{prefix}: {message}"
            self._telegram_bot.send_message(
                chat_id=Config.TELEGRAM_CHAT_ID,
                text=formatted_message
            )
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")

    def _log_session_event(self, event_type: str, message: Optional[str] = None) -> None:
        """Log session event to database."""
        try:
            self._db_manager.log_session_event(
                event_type, self.user_id, message)
        except Exception as e:
            logger.error(f"Failed to log session event: {e}")

    def authenticate(self) -> bool:
        """
        Authenticate with BRAIN API using credentials.
        Handles biometric authentication if required.

        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            # Load credentials
            email, password = self._load_credentials()
            self.session.auth = (email, password)

            # Attempt authentication
            logger.info("Attempting authentication with BRAIN API...")
            response = self.session.post(f"{self.base_url}/authentication")

            if response.status_code == 201:
                # Standard authentication successful
                self._handle_successful_auth(response.json())
                self._send_telegram_notification("Authentication successful")
                self._log_session_event(
                    "login", "Standard authentication successful")
                return True

            elif response.status_code == 401:
                # Check if biometric authentication is required
                if response.headers.get("WWW-Authenticate") == "persona":
                    return self._handle_biometric_auth(response)
                else:
                    error_msg = "Invalid credentials"
                    logger.error(error_msg)
                    self._send_telegram_notification(error_msg, is_error=True)
                    self._log_session_event("error", error_msg)
                    return False
            else:
                error_msg = f"Authentication failed with status {response.status_code}"
                logger.error(error_msg)
                self._send_telegram_notification(error_msg, is_error=True)
                self._log_session_event("error", error_msg)
                return False

        except Exception as e:
            error_msg = f"Authentication error: {str(e)}"
            logger.error(error_msg)
            self._send_telegram_notification(error_msg, is_error=True)
            self._log_session_event("error", error_msg)
            return False

    def _handle_biometric_auth(self, response: requests.Response) -> bool:
        """
        Handle biometric authentication flow.

        Args:
            response: The 401 response that triggered biometric auth

        Returns:
            bool: True if biometric authentication successful
        """
        try:
            # Extract the biometric inquiry URL
            inquiry_url = response.headers.get("Location")
            if not inquiry_url:
                error_msg = "No biometric inquiry URL provided"
                logger.error(error_msg)
                self._send_telegram_notification(error_msg, is_error=True)
                return False

            # Construct full URL
            full_url = urljoin(self.base_url, inquiry_url)

            # Prompt user to complete biometric authentication
            print(f"\n🔐 Biometric authentication required!")
            print(f"Please open this URL in your browser: {full_url}")
            print(
                "Complete the biometric authentication, then press Enter to continue...")

            input("Press Enter after completing biometric authentication...")

            # Verify authentication was successful
            verify_response = self.session.post(full_url)

            if verify_response.status_code == 200:
                # Get the final authentication state
                auth_response = self.session.get(
                    f"{self.base_url}/authentication")
                if auth_response.status_code == 200:
                    self._handle_successful_auth(auth_response.json())
                    self._send_telegram_notification(
                        "Biometric authentication successful")
                    self._log_session_event(
                        "login", "Biometric authentication successful")
                    return True

            error_msg = "Biometric authentication failed"
            logger.error(error_msg)
            self._send_telegram_notification(error_msg, is_error=True)
            self._log_session_event("error", error_msg)
            return False

        except Exception as e:
            error_msg = f"Biometric authentication error: {str(e)}"
            logger.error(error_msg)
            self._send_telegram_notification(error_msg, is_error=True)
            self._log_session_event("error", error_msg)
            return False

    def _handle_successful_auth(self, auth_data: Dict[str, Any]) -> None:
        """Handle successful authentication response."""
        self.user_id = auth_data.get("user", {}).get("id")

        # Extract token expiry
        token_info = auth_data.get("token", {})
        if "expiry" in token_info:
            # Calculate expiry time (expiry is in seconds from now)
            self.token_expiry = datetime.now(
            ) + timedelta(seconds=token_info["expiry"])
            logger.info(f"Session token expires at: {self.token_expiry}")
        else:
            # Default expiry if not provided
            self.token_expiry = datetime.now() + timedelta(hours=1)
            logger.warning("No token expiry provided, assuming 1 hour")

    def is_session_valid(self) -> bool:
        """
        Check if the current session is valid and not expired.

        Returns:
            bool: True if session is valid, False otherwise
        """
        if not self.token_expiry:
            return False

        # Add buffer time to refresh before actual expiry
        refresh_threshold = datetime.now() + timedelta(seconds=Config.SESSION_REFRESH_THRESHOLD)
        return self.token_expiry > refresh_threshold

    def refresh_session(self) -> bool:
        """
        Refresh the session if needed.

        Returns:
            bool: True if session is valid (either refreshed or still valid)
        """
        if self.is_session_valid():
            logger.debug("Session is still valid, no refresh needed")
            return True

        logger.info("Session expired or expiring soon, refreshing...")
        self._log_session_event("refresh", "Session refresh initiated")
        success = self.authenticate()
        if success:
            self._log_session_event("refresh", "Session refresh successful")
        else:
            self._log_session_event("error", "Session refresh failed")
        return success

    def get_session(self) -> requests.Session:
        """
        Get the current session, refreshing if necessary.

        Returns:
            requests.Session: The authenticated session
        """
        if not self.is_session_valid():
            if not self.refresh_session():
                raise RuntimeError("Failed to refresh session")

        return self.session

    def logout(self) -> None:
        """Logout and clear session."""
        try:
            if self.session:
                self.session.delete(f"{self.base_url}/authentication")
            self.token_expiry = None
            self.user_id = None
            logger.info("Logged out successfully")
            self._send_telegram_notification("Logged out successfully")
            self._log_session_event("logout", "Logout successful")
        except Exception as e:
            logger.error(f"Logout error: {e}")
            self._log_session_event("error", f"Logout error: {e}")

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        Get current user information.

        Returns:
            Optional[Dict]: User information or None if not authenticated
        """
        try:
            response = self.get_session().get(
                f"{self.base_url}/authentication")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None


# Convenience function for getting a session handler instance
def get_session_handler() -> BrainSessionHandler:
    """Get a configured session handler instance."""
    return BrainSessionHandler()


if __name__ == "__main__":
    # Example usage
    Config.validate()

    handler = BrainSessionHandler()

    if handler.authenticate():
        print("✅ Authentication successful!")
        print(f"User ID: {handler.user_id}")
        print(f"Token expires: {handler.token_expiry}")

        # Test session validity
        print(f"Session valid: {handler.is_session_valid()}")

        # Test API call
        try:
            session = handler.get_session()
            response = session.get(
                f"{Config.BRAIN_API_BASE_URL}/authentication")
            print(f"API test response: {response.status_code}")
        except Exception as e:
            print(f"API test failed: {e}")
    else:
        print("❌ Authentication failed!")
