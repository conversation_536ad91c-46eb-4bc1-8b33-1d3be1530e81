# BrainSpace Session Handler

A comprehensive session management system for the BRAIN API with biometric authentication support, Telegram notifications, and PostgreSQL persistence.

## Features

- 🔐 **Biometric Authentication**: Handles Persona biometric authentication flow
- 🔄 **Auto-refresh**: Automatically refreshes sessions before expiry
- 📱 **Telegram Notifications**: Real-time notifications for session events
- 🗄️ **Database Logging**: Persistent session event logging
- 🧵 **Thread-safe**: Continuous session monitoring with threading
- ⚙️ **Configurable**: Environment-based configuration

## Architecture

The session handler consists of three main components:

1. **`BrainSessionHandler`**: Core authentication and session management
2. **`SessionManager`**: Continuous session monitoring and auto-refresh
3. **`DatabaseManager`**: PostgreSQL integration for session logging

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Create a `.env` file in the project root:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost/brainspace

# BRAIN API
BRAIN_API_BASE_URL=https://api.worldquantbrain.com
BRAIN_CREDENTIALS_FILE=~/.brain_credentials

# Telegram Bot (optional)
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Session Configuration
SESSION_REFRESH_THRESHOLD=300
SESSION_CHECK_INTERVAL=60

# API Configuration
API_CONCURRENCY_LIMIT=5
API_REQUEST_TIMEOUT=30
```

### 3. Credentials Setup

Create the credentials file at `~/.brain_credentials`:

```json
["<EMAIL>", "your_password"]
```

### 4. Database Setup

```bash
# Initialize database tables
python database.py
```

### 5. Test the Session Handler

```bash
# Run the example script
python example_session_usage.py
```

## Usage

### Basic Session Handler

```python
from session_handler import BrainSessionHandler

# Create session handler
handler = BrainSessionHandler()

# Authenticate
if handler.authenticate():
    print("Authentication successful!")

    # Get authenticated session for API calls
    session = handler.get_session()
    response = session.get("https://api.worldquantbrain.com/authentication")

    # Logout when done
    handler.logout()
```

### Continuous Session Manager

```python
from session_manager import get_session_manager

# Start session manager (runs in background)
manager = get_session_manager()
if manager.start():
    print("Session manager started!")

    # Get current session for API calls
    session = manager.get_session()

    # Check status
    status = manager.get_status()
    print(f"Status: {status}")

    # Stop when done
    manager.stop()
```

### Database Integration

```python
from database import get_database_manager, initialize_database

# Initialize database
initialize_database()

# Get database manager
db_manager = get_database_manager()

# Log session events
db_manager.log_session_event("login", "user123", "Login successful")

# Get recent events
events = db_manager.get_recent_session_events(10)

# Get database stats
stats = db_manager.get_database_stats()
```

## API Reference

### BrainSessionHandler

#### Methods

- `authenticate() -> bool`: Authenticate with BRAIN API
- `is_session_valid() -> bool`: Check if session is valid
- `refresh_session() -> bool`: Refresh session if needed
- `get_session() -> requests.Session`: Get authenticated session
- `logout() -> None`: Logout and clear session
- `get_user_info() -> Optional[Dict]`: Get current user information

### SessionManager

#### Methods

- `start() -> bool`: Start session manager
- `stop() -> None`: Stop session manager
- `get_session() -> requests.Session`: Get current session
- `is_running() -> bool`: Check if manager is running
- `get_status() -> dict`: Get manager status
- `force_refresh() -> bool`: Force session refresh

### DatabaseManager

#### Methods

- `initialize_database() -> bool`: Initialize database tables
- `log_session_event(event_type, user_id, message) -> bool`: Log session event
- `get_recent_session_events(limit) -> List[Dict]`: Get recent events
- `get_alpha_count_by_status() -> Dict[str, int]`: Get alpha status counts
- `get_database_stats() -> Dict`: Get database statistics

## Database Schema

### Tables

#### `template`
- `id`: Primary key
- `template`: Template string
- `created_at`: Creation timestamp

#### `alpha`
- `id`: Primary key
- `alpha`: Generated alpha code
- `status`: Status enum ('none', 'simulating', 'finish', 'error')
- `simulationid`: External API simulation ID
- `result`: Simulation results (JSONB)
- `error`: Error message
- `created_at`: Creation timestamp
- `simulate_at`: Simulation start timestamp
- `result_at`: Result retrieval timestamp

#### `alpha_reference`
- `id`: Primary key
- `alpha_id`: Foreign key to alpha
- `template_id`: Foreign key to template

#### `session_log`
- `id`: Primary key
- `event_type`: Event type (login, logout, refresh, error)
- `user_id`: User ID
- `message`: Event message
- `created_at`: Event timestamp

## Biometric Authentication Flow

1. **Initial Request**: Send authentication request with credentials
2. **Biometric Challenge**: If 401 with `WWW-Authenticate: persona` header
3. **Browser Interaction**: User completes biometric authentication in browser
4. **Verification**: Verify authentication was successful
5. **Session Establishment**: Establish authenticated session

## Error Handling

The session handler includes comprehensive error handling:

- **Authentication Failures**: Logged to database and Telegram
- **Session Expiry**: Automatic refresh with notifications
- **Network Errors**: Retry logic with exponential backoff
- **Database Errors**: Graceful degradation with logging

## Monitoring

### Telegram Notifications

Configure Telegram bot for real-time notifications:

- ✅ Authentication success
- ❌ Authentication failures
- 🔄 Session refresh events
- ⚠️ Error conditions

### Database Logging

All session events are logged to PostgreSQL:

- Login/logout events
- Session refresh attempts
- Error conditions
- User activity tracking

## Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `postgresql://localhost/brainspace` | PostgreSQL connection string |
| `BRAIN_API_BASE_URL` | `https://api.worldquantbrain.com` | BRAIN API base URL |
| `BRAIN_CREDENTIALS_FILE` | `~/.brain_credentials` | Credentials file path |
| `TELEGRAM_BOT_TOKEN` | `None` | Telegram bot token |
| `TELEGRAM_CHAT_ID` | `None` | Telegram chat ID |
| `SESSION_REFRESH_THRESHOLD` | `300` | Seconds before expiry to refresh |
| `SESSION_CHECK_INTERVAL` | `60` | Session check interval in seconds |
| `API_CONCURRENCY_LIMIT` | `5` | API concurrency limit |
| `API_REQUEST_TIMEOUT` | `30` | API request timeout in seconds |

## Development

### Running Tests

```bash
# Run example script
python example_session_usage.py

# Test individual components
python session_handler.py
python session_manager.py
python database.py
```

### Project Structure

```
BrainSpace/
├── config.py              # Configuration management
├── session_handler.py     # Core session handler
├── session_manager.py     # Continuous session manager
├── database.py           # Database integration
├── example_session_usage.py  # Usage examples
├── requirements.txt      # Dependencies
├── README.md            # This file
├── prd.md              # Product requirements
└── doc/
    └── API_Document.md  # BRAIN API documentation
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check credentials file format
   - Verify email/password
   - Check network connectivity

2. **Biometric Authentication**
   - Ensure browser is available
   - Complete authentication in browser
   - Check Persona service status

3. **Database Connection**
   - Verify PostgreSQL is running
   - Check connection string
   - Ensure database exists

4. **Telegram Notifications**
   - Verify bot token and chat ID
   - Check bot permissions
   - Ensure internet connectivity

### Logs

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## License

This project is part of the BrainSpace alpha generation and simulation system.

## Contributing

1. Follow the PRD specifications
2. Maintain thread safety
3. Add comprehensive error handling
4. Update documentation
5. Test with real BRAIN API credentials
